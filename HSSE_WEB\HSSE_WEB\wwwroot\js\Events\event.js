$(document).ready(function () {
    attachRequiredValidation('#Title', 'Title is required');
    attachRequiredValidation('#EventDateTime', 'Please select Event Date');
    attachRequiredValidation('#Location', 'Location is required');

    // Initial check on page load
    checkRequiredFields();
    function loadEvents() {
        const table = $('#order-listing');

        // Destroy DataTable if already initialized
        if ($.fn.DataTable.isDataTable(table)) {
            table.DataTable().clear().destroy();
        }

        // Clear table body before refill
        const tbody = table.find('tbody');
        tbody.empty();
        $.ajax({
            url: getEvents,
            type: 'GET',
            success: function (response) {
                var events = response.data || [];
                var tbody = $('#order-listing tbody');
                tbody.empty();
                events.forEach(function (event, index) {
           
                    const actionButton =`<a  class="dropdown-item btn-toggle-active" title="Delete" data-event-id="${event.eventId}">
                             <i class="mdi mdi-delete text-danger"></i> Delete
                           </a>`;
                    const IsRsvp = event.isRsvp
                        ? '<label class="badge badge-success">Yes</label>'
                        : '<label class="badge badge-danger">No</label>';
                    const rsvpButton = event.isRsvp
                        ? `<a class="dropdown-item view-rsvp-btn" onclick="rsvpList(${event.eventId})"" title="RSVP List">
         <i class="mdi mdi-account-multiple-outline text-info"></i> RSVP List
       </a>`
                        : '';

                    const editButton = `
    <div class="dropdown d-inline">
        <button class="btn btn-sm btn-outline-primary" type="button" id="dropdownStatus${event.eventId}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
          <i class="fa fa-gear"></i>
        </button>
        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownStatus${event.eventId}">
          
           <a  class="dropdown-item btn-edit-event" title="Edit" data-event-id="${event.eventId}">
                                    <i class="mdi mdi-pencil text-primary"></i> Edit
                                </a>
              ${actionButton}
                             ${rsvpButton}
        </div>
    </div>
`;
                    var row = `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${event.title || ''}</td>
                         <td>${
                        (event.description || '')
                            .split(' ')
                            .slice(0, 6)
                            .join(' ')
                        }${(event.description || '').split(' ').length > 6 ? '...' : ''}</td>
                            <td>${event.eventDateTime ? new Date(event.eventDateTime).toLocaleString() : ''}</td>
                            <td>${event.location || ''}</td>
                              <td>${IsRsvp}</td>

                          
                            <td>
                            ${editButton}
                            
                            
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
                initializeDataTable(table);
            },
            error: function (xhr) {
                let msg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : 'Error getting events.';
                showDangerToast(msg);
            }
        });
    }


    loadEvents();

    $('#eventForm').on('submit', async function (e) {
        e.preventDefault();

        // Get description from TinyMCE with fallback
        var description = '';
        try {
            var tinyMceInstance = tinymce.get('tinyMceExample');
            if (tinyMceInstance) {
                description = tinyMceInstance.getContent();
            } else {
                description = $('#tinyMceExample').val();
            }
        } catch (error) {
            console.warn('TinyMCE not available, using textarea value:', error);
            description = $('#tinyMceExample').val();
        }

        var groupId = $('#GroupId').val()?.map(id => parseInt(id)) || [];

        const fileInput = $('#MediaFile')[0];
        let base64File = null;
        let fileName = null;

        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];
            base64File = await toBase64(file); // base64 string including data:mime
            fileName = file.name;
        }

        const attachmentInput = $('#Attachment')[0];
        let attachmentBase64File = null;
        let attachmentFileName = null;

        if (attachmentInput.files.length > 0) {
            const attachmentFile = attachmentInput.files[0];
            attachmentBase64File = await toBase64(attachmentFile); // base64 string including data:mime
            attachmentFileName = attachmentFile.name;
        }
        var Rsvp = $('#IsRsvp').is(':checked'); // Convert checkbox to integer
        const eventData = {
            eventId: 0,
            title: $('#Title').val(),
            description: description,
            mediaUrl: base64File,     // Base64 string
            fileName: fileName,       // Needed by backend to extract original name
            eventDateTime: $('#EventDateTime').val() ? new Date($('#EventDateTime').val()).toISOString() : null,
            location: $('#Location').val(),
            externalLink: $('#ExternalLink').val(),
            createdAt: new Date().toISOString(),
            scheduleAt: $('#ScheduledAt').val() ? new Date($('#ScheduledAt').val()).toISOString() : null,
            expiryAt: $('#ExpiryAt').val() ? new Date($('#ExpiryAt').val()).toISOString() : null,
            groupId: groupId,
            isRsvp: Rsvp,
            attachment: attachmentBase64File
        };

        console.log('Submitting event data:', eventData);

        $.ajax({
            url: InsertOrUpdateEvent,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(eventData),
            beforeSend: function() {
                console.log('Sending request to:', InsertOrUpdateEvent);
            },
            success: function (response) {
                console.log('Event created successfully:', response);
                showSuccessToast(response.message || 'Event created successfully');
                $('#eventForm')[0].reset();
                $('#GroupId').val(null).trigger('change');
                $('#ThumbnailFileName').val('');
                $('#MediaFile').val('');
                $('#ThumbnailPreview').hide();
                $('#ThumbnailPreviewContainer').addClass('d-none');
                $('#removeThumbnailBtn').addClass('d-none');
                // Clear attachment fields
                $('#AttachmentFileName').val('');
                $('#Attachment').val('');
                $('#AttachmentPreviewName').text('');
                $('#AttachmentPreviewContainer').addClass('d-none');
                $('#removeAttachmentBtn').addClass('d-none');
                // Clear TinyMCE content
                try {
                    var tinyMceInstance = tinymce.get('tinyMceExample');
                    if (tinyMceInstance) {
                        tinyMceInstance.setContent('');
                    }
                } catch (error) {
                    console.warn('Could not clear TinyMCE content:', error);
                }
                loadEvents();
            },
            error: function (xhr) {
                console.error('Error creating event:', xhr);
                let msg = 'Error creating event.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    msg = xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        msg = errorResponse.message || msg;
                    } catch (e) {
                        msg = xhr.responseText;
                    }
                }
                showDangerToast(msg);
            }
        });
    });

    // Add handler for toggle activation button
    $(document).on('click', '.btn-toggle-active', function () {
        var btn = $(this);
        var eventId = btn.data('event-id');
        btn.prop('disabled', true);
        $.ajax({
            url: toggleEventActivation,
            type: 'POST',
            data: { eventId: eventId },
            success: function (response) {
                showSuccessToast(response.message || 'Event activation toggled.');
                // Reload events to update table
                loadEvents();
            },
            error: function (xhr) {
                let msg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : 'Failed to toggle activation.';
                showDangerToast(msg);
                btn.prop('disabled', false);
            }
        });
    });

    // Edit event button handler
    $(document).on('click', '.btn-edit-event', function () {
        var eventId = $(this).data('event-id');
        $.ajax({
            url: getEventById  + eventId,
            type: 'GET',
            success: function (response) {
                var event = response.data;
                if (!event) return showDangerToast('Event not found.');
                $('#editEventId').val(event.eventId);
                $('#editTitle').val(event.title);
                $('#editExternalLink').val(event.externalLink);
                $('#editLocation').val(event.location);
                $('#editEventDateTime').val(event.eventDateTime ? event.eventDateTime.substring(0, 16) : '');
                $('#editScheduledAt').val(event.scheduleAt ? event.scheduleAt.substring(0, 16) : '');
                $('#editExpiryAt').val(event.expiryAt ? event.expiryAt.substring(0, 16) : '');
                $('#editIsRsvp').prop('checked', event.isRsvp);
                tinymce.get('editTinyMceExample').setContent(event.description || '');
                // Set group selection from MstAnnouncementReceivers
                if (event.mstAnnouncementReceivers && Array.isArray(event.mstAnnouncementReceivers)) {
                    var groupIds = event.mstAnnouncementReceivers
                        .map(r => r.groupId ? String(r.groupId) : null)
                        .filter(Boolean);
                    $('#editGroupId').val(groupIds).trigger('change');
                } else {
                    $('#editGroupId').val(null).trigger('change');
                }
                // Thumbnail preview
                if (event.mediaUrl) {
                    $('#editThumbnailPreview').attr('src', event.mediaUrl).show();
                    $('#editThumbnailPreviewContainer').removeClass('d-none');
                    $('#editThumbnailFileName').val(event.fileName || '');
                    $('#removeEditThumbnailBtn').removeClass('d-none');
                } else {
                    $('#editThumbnailPreview').attr('src', '').hide();
                    $('#editThumbnailPreviewContainer').addClass('d-none');
                    $('#editThumbnailFileName').val('');
                    $('#removeEditThumbnailBtn').addClass('d-none');
                }
                let attachmentFullPath = event.attachment || '';
                if (attachmentFullPath) {
                    $('#EditAttachmentFileName').val(attachmentFullPath.split('/').pop()); // just show the file name
                    $('#ExistingAttachment').val(attachmentFullPath || '');
                    $('#removeAttachmentThumbnailBtn').removeClass('d-none');
                } else {
                    $('#EditAttachmentFileName').val('');
                    $('#removeAttachmentBtn').addClass('d-none');
                }
                $('#editEventModal').modal('show');
            },
            error: function () {
                showDangerToast('Failed to load event details.');
            }
        });
    });

    // Edit thumbnail upload logic
    $('#editThumbnailBtn').click(function () {
        $('#editMediaFile').click();
    });
    $('#editMediaFile').on('change', function (e) {
        const file = e.target.files[0];
        if (file) {
            $('#editThumbnailFileName').val(file.name);
            const reader = new FileReader();
            reader.onload = function (e) {
                $('#editThumbnailPreview').attr('src', e.target.result);
                $('#editThumbnailPreviewContainer').removeClass('d-none');
                $('#removeEditThumbnailBtn').removeClass('d-none');
            };
            reader.readAsDataURL(file);
        }
    });
    $('#removeEditThumbnailBtn').click(function () {
        $('#editMediaFile').val('');
        $('#editThumbnailFileName').val('');
        $('#editThumbnailPreview').attr('src', '').hide();
        $('#editThumbnailPreviewContainer').addClass('d-none');
        $(this).addClass('d-none');
    });

    // Save edited event
    $('#saveEditEventBtn').click(async function () {
        var description = tinymce.get('editTinyMceExample').getContent();
        var groupId = $('#editGroupId').val()?.map(id => parseInt(id)) || [];
        const fileInput = $('#editMediaFile')[0];
        let base64File = null;
        let fileName = null;
        if (fileInput.files.length > 0) {
            const file = fileInput.files[0];
            base64File = await toBase64(file);
            fileName = file.name;
        } else {
            base64File = $('#editThumbnailPreview').attr('src') || null;
            fileName = $('#editThumbnailFileName').val() || null;
        }

        const attachmentInput = $('#EditAttachment')[0];
        let attachmentBase64File = null;
        let attachmentFileName = null;

        if (attachmentInput.files.length > 0) {
            const attachmentFile = attachmentInput.files[0];
            attachmentBase64File = await toBase64(attachmentFile); // base64 string including data:mime
            attachmentFileName = attachmentFile.name;
        }
        else {
            attachmentBase64File = $('#ExistingAttachment').val() || null;
        }
        var Rsvp = $('#editIsRsvp').is(':checked');
        const eventData = {
            eventId: parseInt($('#editEventId').val()),
            title: $('#editTitle').val(),
            description: description,
            mediaUrl: base64File,
            fileName: fileName,
            eventDateTime: $('#editEventDateTime').val() ? new Date($('#editEventDateTime').val()).toISOString() : null,
            location: $('#editLocation').val(),
            externalLink: $('#editExternalLink').val(),
            scheduleAt: $('#editScheduledAt').val() ? new Date($('#editScheduledAt').val()).toISOString() : null,
            expiryAt: $('#editExpiryAt').val() ? new Date($('#editExpiryAt').val()).toISOString() : null,
            groupId: groupId,
            isRsvp: Rsvp,
            attachment: attachmentBase64File,
            modifiedAt: new Date().toISOString(),
        };
        $.ajax({
            url: InsertOrUpdateEvent,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(eventData),
            success: function (response) {
                showSuccessToast('Event updated successfully!');
                $('#editEventModal').modal('hide');
                loadEvents();
            },
            error: function (xhr) {
                let msg = xhr.responseJSON && xhr.responseJSON.message ? xhr.responseJSON.message : 'Error updating event.';
                showDangerToast(msg);
            }
        });
    });
}); 
function rsvpList(eventId) {
    $.ajax({
        url: getEventRsvpDetails + eventId,
        method: 'GET',
        success: function (response) {
            const users = response.data || [];
            const userList = $('#rsvpUserList');
            userList.empty();

            const ul = $('<ul class="list-group list-group-flush"></ul>');

            if (users.length > 0) {
                users.forEach(user => {
                    ul.append(`<li class="list-group-item">${user.userName || 'Unnamed User'}</li>`);
                });
            } else {
                ul.append('<li class="list-group-item text-muted">No users registered yet.</li>');
            }

            userList.append(ul);
            $('#rsvpModal').modal('show');
        },
        error: function () {
            alert('Failed to load RSVP users.');
        }
    });
}


function toBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}
// Trigger hidden file input
$('#ThumbnailBtn').click(function () {
    $('#MediaFile').click();
});

// File selection
$('#MediaFile').on('change', function (e) {
    const file = e.target.files[0];
    if (file) {
        $('#ThumbnailFileName').val(file.name);

        const reader = new FileReader();
        reader.onload = function (e) {
            $('#ThumbnailPreview').attr('src', e.target.result);
            $('#ThumbnailPreviewContainer').removeClass('d-none');
            $('#removeThumbnailBtn').removeClass('d-none');
        };
        reader.readAsDataURL(file);
    }
});

// Remove selected image
$('#removeThumbnailBtn').click(function () {
    $('#AnnouncementFile').val('');
    $('#ThumbnailFileName').val('');
    $('#ThumbnailPreview').attr('src', '');
    $('#ThumbnailPreviewContainer').addClass('d-none');
    $(this).addClass('d-none');
});

$('#AttachmentBtn').click(function () {
    $('#Attachment').click();
});

// File selection
$('#Attachment').on('change', function (e) {
    const file = e.target.files[0];
    if (file) {
        $('#AttachmentFileName').val(file.name);
        $('#AttachmentPreviewName').text(file.name);
        $('#AttachmentPreviewContainer').removeClass('d-none');
        $('#removeAttachmentBtn').removeClass('d-none');
    }
});

// Remove selected attachment
$('#removeAttachmentBtn').click(function () {
    $('#Attachment').val('');
    $('#AttachmentFileName').val('');
    $('#AttachmentPreviewName').text('');
    $('#AttachmentPreviewContainer').addClass('d-none');
    $(this).addClass('d-none');
});

$('#EditAttachmentBtn').click(function () {
    $('#EditAttachment').click();
});

// File selection
$('#EditAttachment').on('change', function (e) {
    const file = e.target.files[0];
    if (file) {
        $('#EditAttachmentFileName').val(file.name);
        const reader = new FileReader();
        reader.readAsDataURL(file);
    }
});

// Remove selected attachment in edit modal
$('#removeEditAttachmentBtn').click(function () {
    $('#EditAttachment').val('');
    $('#EditAttachmentFileName').val('');
    $('#ExistingAttachment').val('');
    $(this).addClass('d-none');
});