/**
 * Enhanced Date Picker Functionality
 * Makes date input controls fully clickable to open date picker popup
 * Supports both date and datetime-local input types
 */

(function($) {
    'use strict';

    // Initialize enhanced date picker functionality
    function initEnhancedDatePickers() {
        // Target all date and datetime-local inputs
        const dateInputs = $('input[type="date"], input[type="datetime-local"]');
        
        dateInputs.each(function() {
            const $input = $(this);
            
            // Skip if already enhanced
            if ($input.hasClass('enhanced-datepicker')) {
                return;
            }
            
            // Mark as enhanced
            $input.addClass('enhanced-datepicker');
            
            // Create wrapper if not already wrapped
            if (!$input.parent().hasClass('enhanced-datepicker-wrapper')) {
                $input.wrap('<div class="enhanced-datepicker-wrapper position-relative"></div>');
            }
            
            const $wrapper = $input.parent('.enhanced-datepicker-wrapper');
            
            // Add calendar icon if not present
            if ($wrapper.find('.datepicker-icon').length === 0) {
                $wrapper.append('<i class="datepicker-icon fas fa-calendar-alt"></i>');
            }
            
            // Make the entire wrapper clickable
            $wrapper.off('click.enhanced-datepicker').on('click.enhanced-datepicker', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Focus and trigger click on the actual input
                $input.focus();
                
                // For browsers that support showPicker
                if ($input[0].showPicker) {
                    try {
                        $input[0].showPicker();
                    } catch (error) {
                        // Fallback for browsers that don't support showPicker
                        $input.trigger('click');
                    }
                } else {
                    // Fallback method
                    $input.trigger('click');
                }
            });
            
            // Prevent double-clicking issues
            $input.off('click.enhanced-datepicker').on('click.enhanced-datepicker', function(e) {
                e.stopPropagation();
            });
            
            // Handle keyboard navigation
            $input.off('keydown.enhanced-datepicker').on('keydown.enhanced-datepicker', function(e) {
                // Space or Enter key opens the picker
                if (e.keyCode === 32 || e.keyCode === 13) {
                    e.preventDefault();
                    if (this.showPicker) {
                        try {
                            this.showPicker();
                        } catch (error) {
                            $(this).trigger('click');
                        }
                    } else {
                        $(this).trigger('click');
                    }
                }
            });
        });
    }

    // Initialize Bootstrap datepicker for better browser compatibility
    function initBootstrapDatePickers() {
        // Initialize for inputs with specific classes
        if ($.fn.datepicker) {
            $('.datepicker-autoclose').datepicker({
                autoclose: true,
                todayHighlight: true,
                format: 'dd-mm-yyyy'
            });
            
            $('.datepicker-popup').datepicker({
                enableOnReadonly: true,
                todayHighlight: true,
                format: 'dd-mm-yyyy'
            });
            
            // Handle date range inputs
            $('.input-daterange').datepicker({
                autoclose: true,
                todayHighlight: true,
                format: 'dd-mm-yyyy'
            });
        }
    }

    // Initialize datetime pickers
    function initDateTimePickers() {
        if ($.fn.datetimepicker) {
            $('.datetime-picker').datetimepicker({
                format: 'DD-MM-YYYY HH:mm',
                showTodayButton: true,
                showClear: true,
                showClose: true
            });
        }
    }

    // Add CSS styles for enhanced date picker
    function addDatePickerStyles() {
        const styles = `
            <style id="enhanced-datepicker-styles">
                .enhanced-datepicker-wrapper {
                    cursor: pointer;
                    position: relative;
                }
                
                .enhanced-datepicker-wrapper input[type="date"],
                .enhanced-datepicker-wrapper input[type="datetime-local"] {
                    cursor: pointer;
                    padding-right: 35px;
                }
                
                .enhanced-datepicker-wrapper .datepicker-icon {
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #6c757d;
                    pointer-events: none;
                    z-index: 2;
                }
                
                .enhanced-datepicker-wrapper:hover .datepicker-icon {
                    color: #007bff;
                }
                
                .enhanced-datepicker-wrapper input:focus + .datepicker-icon {
                    color: #007bff;
                }
                
                /* Hide native date picker icon in webkit browsers */
                .enhanced-datepicker-wrapper input[type="date"]::-webkit-calendar-picker-indicator,
                .enhanced-datepicker-wrapper input[type="datetime-local"]::-webkit-calendar-picker-indicator {
                    opacity: 0;
                    position: absolute;
                    right: 0;
                    width: 100%;
                    height: 100%;
                    cursor: pointer;
                }
                
                /* Ensure the input is fully clickable */
                .enhanced-datepicker-wrapper input[type="date"],
                .enhanced-datepicker-wrapper input[type="datetime-local"] {
                    position: relative;
                    z-index: 1;
                }
            </style>
        `;
        
        if ($('#enhanced-datepicker-styles').length === 0) {
            $('head').append(styles);
        }
    }

    // Main initialization function
    function initialize() {
        addDatePickerStyles();
        initEnhancedDatePickers();
        initBootstrapDatePickers();
        initDateTimePickers();
    }

    // Initialize on document ready
    $(document).ready(function() {
        initialize();
        
        // Re-initialize when new content is added dynamically
        $(document).on('DOMNodeInserted', function(e) {
            if ($(e.target).find('input[type="date"], input[type="datetime-local"]').length > 0) {
                setTimeout(initEnhancedDatePickers, 100);
            }
        });
        
        // Modern alternative for mutation observer
        if (window.MutationObserver) {
            const observer = new MutationObserver(function(mutations) {
                let shouldReinit = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) { // Element node
                                const $node = $(node);
                                if ($node.is('input[type="date"], input[type="datetime-local"]') || 
                                    $node.find('input[type="date"], input[type="datetime-local"]').length > 0) {
                                    shouldReinit = true;
                                }
                            }
                        });
                    }
                });
                
                if (shouldReinit) {
                    setTimeout(initEnhancedDatePickers, 100);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    });

    // Expose functions globally for manual initialization
    window.EnhancedDatePicker = {
        init: initialize,
        initDatePickers: initEnhancedDatePickers,
        addStyles: addDatePickerStyles
    };

})(jQuery);
